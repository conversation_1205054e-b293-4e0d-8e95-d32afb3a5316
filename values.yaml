paaslab:
  paaslab: panji
replicaCount: 1
PanjiSourceLabel:
  Labels:
    - 'operation-source: api'
    - 'paas-app-code: pj-yizn-zksync'
    - 'paas-app-service-version: v1'
    - 'paas-app-source: helm'
    - 'paas-cluster-code: "63"'
    - 'paas-env-code: PROD'
    - 'paas-owner: xdlxuechen'
    - 'paas-plane-code: wxgxjq'
    - 'paas-resource-category: tenant-app'
    - 'paas-system-code: jfzw-yizn'
    - 'paas-tenant-code: xydjfzwzx-yz'
    - 'paas-unit-code: jfyzzx'
    - 'paas-workload-name: pj-yizn-zksync'
namespace:
  name: jfzw-yizn
image:
  imageName: zksync.arm
  tag: 250630.0.1
  repository: 10.33.225.29:1121/jxmk_image_repo
  pullPolicy: Always
containerCommand:
  startup:
    command:
      - 'sh'
    args:
      - '/karaf/start0.sh'
configmap:
  name: zksync-cp
  configdata:
    - 'DB_PD_URL: "http://169.169.191.188:40100/securityserver-1.0-SNAPSHOT/security/querydbuserinfo.do"'
    - 'CITY_NAME: "ZJ"'
    - 'ZK: "10.177.71.116:2402,10.177.71.131:2402,10.177.71.191:2402,10.177.71.227:2402,10.177.71.239:2402"'
    - 'PROCESS_NAME: "zksync"'
    - 'kafka_topic: "stream_billing_saturn"'
    - 'kafka_addr: "10.32.211.22:42624,10.32.211.21:33588,10.32.210.76:56446,10.32.211.33:41104,10.32.211.26:43456"'
    - 'NODE_INCREMENT: "0"'
resources:
  requests:
    memory: 4Gi
    cpu: 2
  limits:
    memory: 6Gi
    cpu: 4
strategy:
  rollingUpdateisUsed: false
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%
livenessProbe:
  livenessProbeCommand:
    command:
      - 'ls'
  failureThreshold: 3
  periodSeconds: 120
  timeoutSeconds: 10
  successThreshold: 1
  initialDelaySeconds: 100
  livenessProbeisUsed: true
readinessProbe:
  readinessProbeCommand:
    command:
      - 'ls'
  failureThreshold: 3
  readinessProbeisUsed: true
  periodSeconds: 120
  timeoutSeconds: 10
  successThreshold: 1
  initialDelaySeconds: 100
useVolumesPVC:
  enable: false
volumesPVC:
  - volumesName: "bossdata03"
    mountPath: '/bossdata03'
    podMountPath: pod内挂载路径
    useVolumesType: "hostPath"
    volumesClaimName: "存储卷名称"
    volumesPVCUsed: false
    podmountPath: '/bossdata03'
env:
  app_name: pj-yizn-zksync
dv:
  sidecar_istio: false
