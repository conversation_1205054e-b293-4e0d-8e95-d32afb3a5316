apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
  labels:
    {{- if eq .Values.paaslab.paaslab "panji" }}
    {{- range .Values.PanjiSourceLabel.Labels }}
    {{ . }}
    {{ else }}
    {{- end}}
    {{- end}}
    hwsApplicationVersion: ""
  name: {{ .Values.env.app_name }}
  namespace: {{ .Values.namespace.name }}
spec:
  progressDeadlineSeconds: 600
  replicas: {{ .Values.replicaCount }}
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      {{- if eq .Values.paaslab.paaslab "panji" }}
      {{- range .Values.PanjiSourceLabel.Labels }}
      {{ . }}
      {{ else }}
      {{- end}}
      {{- end}}
      hwsApplicationVersion: ""
  strategy:
    {{- if eq .Values.strategy.rollingUpdateisUsed true }}
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: {{ .Values.strategy.rollingUpdate.maxUnavailable}}
      maxSurge: {{ .Values.strategy.rollingUpdate.maxSurge }}
    {{- else}}
    type: Recreate
    {{- end }}
  template:
    metadata:
      labels:
        {{- if eq .Values.paaslab.paaslab "panji" }}
        {{- range .Values.PanjiSourceLabel.Labels }}
        {{ . }}
        {{ else }}
        {{- end}}
        {{- end}}
        hwsApplicationVersion: ""
        sidecar.istio.io/inject: '{{ .Values.dv.sidecar_istio }}'
    spec:
      containers:
      - name: {{ .Values.env.app_name }}
        command: 
        {{- range .Values.containerCommand.startup.command}}
        - {{ . }}
        {{- end }}
        args:
        {{- range .Values.containerCommand.startup.args}}
        - {{ . }}
        {{- end }}
        env:
        - name: DB_PD_URL
          valueFrom:
            configMapKeyRef:
              key: DB_PD_URL
              name: {{ .Values.configmap.name }}
        - name: CITY_NAME
          valueFrom:
            configMapKeyRef:
              key: CITY_NAME
              name: {{ .Values.configmap.name }}
        - name: ZK
          valueFrom:
            configMapKeyRef:
              key: ZK
              name: {{ .Values.configmap.name }}
        - name: PROCESS_NAME
          valueFrom:
            configMapKeyRef:
              key: PROCESS_NAME
              name: {{ .Values.configmap.name }}
        - name: kafka_topic
          valueFrom:
            configMapKeyRef:
              key: kafka_topic
              name: {{ .Values.configmap.name }}
        - name: kafka_addr
          valueFrom:
            configMapKeyRef:
              key: kafka_addr
              name: {{ .Values.configmap.name }}
        - name: NODE_INCREMENT
          valueFrom:
            configMapKeyRef:
              key: NODE_INCREMENT
              name: {{ .Values.configmap.name }}
        image: "{{ .Values.image.repository }}/{{ .Values.image.imageName }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- if eq .Values.useVolumesPVC.enable true }}
        volumeMounts:
        {{- range .Values.volumesPVC }}
          - name: {{ .volumesName }}
            mountPath: {{ .podmountPath }}
        {{- end }}
        {{- end }}
        {{- if eq .Values.livenessProbe.livenessProbeisUsed true }}
        livenessProbe:
          exec:
            command: 
            {{- range .Values.livenessProbe.livenessProbeCommand.command}}
            - {{ . }}
            {{- end }}
          initialDelaySeconds: {{.Values.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{.Values.livenessProbe.periodSeconds }}
          timeoutSeconds: {{.Values.livenessProbe.timeoutSeconds }}
          failureThreshold: {{.Values.livenessProbe.failureThreshold }}
          successThreshold: {{.Values.livenessProbe.successThreshold }}
          {{- end }}
        name: {{ .Values.env.app_name }}
        {{- if eq .Values.readinessProbe.readinessProbeisUsed true }}
        readinessProbe:
          exec:
            command: 
            {{- range .Values.readinessProbe.readinessProbeCommand.command}}
            - {{ . }}
            {{- end }}
          initialDelaySeconds: {{.Values.readinessProbe.initialDelaySeconds }}
          timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
          periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
          failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          successThreshold: {{ .Values.readinessProbe.successThreshold }}
          {{- end}}
        resources:
          limits:
            cpu: {{ .Values.resources.limits.cpu }}
            memory: {{ .Values.resources.limits.memory }}
          requests:
            cpu: {{ .Values.resources.requests.cpu }}
            memory: {{ .Values.resources.requests.memory }}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      {{- if eq .Values.useVolumesPVC.enable true }}
      volumes:
      {{- range .Values.volumesPVC }}
        - name: {{ .volumesName }}
        {{- if eq .useVolumesType "persistentPvc" }}
          persistentVolumeClaim:
            claimName: {{ .volumesClaimName }}
        {{- else if eq .useVolumesType "hostPath" }}
          hostPath:
            path: {{ .mountPath }}
            type: ''
        {{- end }}
      {{- end }}
      {{- end }}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30

